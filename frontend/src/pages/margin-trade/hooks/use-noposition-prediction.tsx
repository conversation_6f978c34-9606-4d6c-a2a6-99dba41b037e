import {
  LeverageObligation,
  LeverageOperation,
  Market,
  Decimal
} from '@pebble-protocol/pebble-sdk'

import { NumberCell } from '@/components/number-cell'
import { formatUnits } from '@/lib/units'
import { useCallback, useMemo } from 'react'
import { useLeverageClient } from './use-leverage-client'
import { useQuery } from '@tanstack/react-query'
import type { Token } from '@/components/balance-input'
import { useBorrow } from '@/hooks/use-borrow'
import { useMarketOperationInfo } from '@/pages/market/hooks/use-market-operation-info'
import { useMarketInfo } from '@/hooks/use-market'
import useMarketTokenInfo from '@/pages/market/hooks/use-market-token-info'
import { useMarketTokenApy } from '@/pages/market/hooks/use-market-apy'
import BigNumber from 'bignumber.js'

interface MarginTradePredictionData {
  id: string
  label: string
  value: string | React.ReactNode
  details?: Array<{
    id: string
    label: string
    value: string | React.ReactNode
  }>
}

interface PredictionResult {
  netBorrowApy?: number
  leverageValue: number
  liquidationPrice: number
  priceChangePercent: number
  netValue: number
  collateralDeposit: {
    amount: () => bigint
    price: () => Decimal
  }
  debtBorrow: {
    amount: () => bigint
    price: () => Decimal
  }
  collateralDecimals: number
  debtDecimals: number
  currentLTV: number
  maxLTV: number
  liquidationLTV?: number
  collateralTokenSymbol: string
  debtTokenSymbol: string
  collateralTokenType: string
  debtTokenType: string
}

export const useMarginTradePrediction = (
  fromMarket: string,
  inputAmount: bigint | undefined,
  leverage: number | undefined,
  collateralType: string | undefined,
  debtType: string | undefined,
  operationType: 'long' | 'short' | undefined,
  selectedToken?: Token,
  token0?: Token,
  token1?: Token
) => {
  const leverageClient = useLeverageClient()

  const marketInfoData = useMarketInfo(fromMarket)
  const { marketInfo } = useMarketOperationInfo(
    fromMarket,
    debtType?.replace('0x', '') || ''
  )
  const market = useMemo(() => {
    return marketInfoData?.market
  }, [marketInfoData])

  const { liqLtv, maxLtv } = useBorrow(
    fromMarket,
    marketInfo?.token || '',
    marketInfo
  )

  const token0Address = useMemo(() => {
    return token0?.coinType.replace('0x', '') || ''
  }, [token0])

  const { marketTokenInfo: tokenInfo0 } = useMarketTokenInfo(
    fromMarket,
    token0Address
  )
  const utilization0 = useMemo(() => {
    if (!tokenInfo0) return 0
    return Number(Number(tokenInfo0?.utilization).toFixed(4))
  }, [tokenInfo0])
  const { borrowApy } = useMarketTokenApy(
    fromMarket,
    token0Address,
    utilization0
  )

  const queryFn = useCallback(async (): Promise<PredictionResult | null> => {
    if (
      !leverageClient ||
      !market ||
      !inputAmount ||
      !leverage ||
      !collateralType ||
      !debtType ||
      !operationType
    ) {
      return null
    }

    try {
      return await calculateMarginTradePredictionData(
        market,
        inputAmount,
        leverage,
        collateralType,
        debtType,
        operationType,
        selectedToken,
        token0,
        token1
      )
    } catch (error) {
      console.error('Failed to calculate prediction data:', error)
      return null
    }
  }, [
    collateralType,
    debtType,
    inputAmount,
    leverage,
    leverageClient,
    market,
    operationType,
    selectedToken,
    token0,
    token1
  ])

  const res = useQuery({
    queryKey: [
      'margin-trade-prediction',
      Number(inputAmount),
      leverage,
      collateralType,
      debtType,
      operationType,
      selectedToken?.symbol,
      token0?.symbol,
      token1?.symbol
    ],
    queryFn,
    enabled: !!(
      leverageClient &&
      inputAmount &&
      leverage &&
      collateralType &&
      debtType &&
      operationType
    )
  })

  const dataWithBorrowApy = res.data
    ? {
        ...res.data,
        netBorrowApy: (borrowApy || 0) * 100,
        liquidationLTV: liqLtv,
        maxLtv: (maxLtv || 0) * 100
      }
    : null

  return {
    ...res,
    predictionData: dataWithBorrowApy
      ? createPredictionData(dataWithBorrowApy)
      : []
  }
}

function createPredictionData(
  data: PredictionResult
): MarginTradePredictionData[] {
  const {
    netBorrowApy,
    leverageValue,
    liquidationPrice,
    priceChangePercent,
    netValue,
    collateralDeposit,
    debtBorrow,
    collateralDecimals,
    debtDecimals,
    currentLTV,
    maxLTV,
    liquidationLTV,
    collateralTokenSymbol,
    debtTokenSymbol
  } = data

  return [
    {
      id: 'netBorrowApy',
      label: 'Net Borrow APY',
      value: (
        <span className="text-primary">
          <NumberCell
            value={netBorrowApy}
            options={{
              maximumFractionDigitsGreater1: 2,
              maximumFractionDigitsLess1: 2,
              minimumFractionDigitsGreater1: 2,
              minimumFractionDigitsLess1: 2
            }}
          />
          %
        </span>
      )
    },
    {
      id: 'leverage',
      label: 'Leverage',
      value: (
        <span className="text-nowrap">
          <NumberCell
            value={leverageValue}
            options={{
              maximumFractionDigitsGreater1: 1,
              maximumFractionDigitsLess1: 1,
              minimumFractionDigitsGreater1: 1,
              minimumFractionDigitsLess1: 1
            }}
          />
          x
        </span>
      )
    },
    {
      id: 'liquidationPrice',
      label: 'Liquidation Price',
      value: (
        <span className="text-nowrap">
          <NumberCell
            value={liquidationPrice}
            options={{
              maximumFractionDigitsGreater1: 2,
              maximumFractionDigitsLess1: 2,
              minimumFractionDigitsGreater1: 2,
              minimumFractionDigitsLess1: 2
            }}
          />{' '}
          (
          <NumberCell
            value={priceChangePercent}
            options={{
              maximumFractionDigitsGreater1: 4,
              maximumFractionDigitsLess1: 4,
              minimumFractionDigitsGreater1: 4,
              minimumFractionDigitsLess1: 4
            }}
          />
          %)
        </span>
      )
    },
    {
      id: 'netValue',
      label: 'Net Value',
      value: (
        <span className="text-nowrap">
          0 → $
          <NumberCell
            value={netValue}
            options={{
              maximumFractionDigitsGreater1: 2,
              maximumFractionDigitsLess1: 2,
              minimumFractionDigitsGreater1: 2,
              minimumFractionDigitsLess1: 2
            }}
          />
        </span>
      ),
      details: [
        {
          id: 'token0Collateral',
          label: `${collateralTokenSymbol} Collateral`,
          value: (
            <span className="text-nowrap">
              0 →{' '}
              <NumberCell
                value={formatUnits(
                  collateralDeposit.amount(),
                  collateralDecimals
                )}
              />
            </span>
          )
        },
        {
          id: 'token1Collateral',
          label: `${debtTokenSymbol} Debt`,
          value: (
            <>
              0 →{' '}
              <NumberCell
                value={formatUnits(debtBorrow.amount(), debtDecimals)}
              />
            </>
          )
        }
      ]
    },
    {
      id: 'loanToValueRatio',
      label: 'Loan-to-Value Ratio',
      value: (
        <span className="text-nowrap">
          0 →{' '}
          <span className="text-green">
            <NumberCell
              value={currentLTV * 100}
              options={{
                maximumFractionDigitsGreater1: 2,
                maximumFractionDigitsLess1: 2,
                minimumFractionDigitsGreater1: 2,
                minimumFractionDigitsLess1: 2
              }}
            />
            %
          </span>
        </span>
      ),
      details: [
        {
          id: 'maxLtv',
          label: 'Max LTV',
          value: (
            <span className="text-primary text-nowrap">
              <NumberCell
                value={maxLTV * 100}
                options={{
                  maximumFractionDigitsGreater1: 2,
                  maximumFractionDigitsLess1: 2,
                  minimumFractionDigitsGreater1: 2,
                  minimumFractionDigitsLess1: 2
                }}
              />
              %
            </span>
          )
        },
        {
          id: 'liquidationLtv',
          label: 'Liq. LTV',
          value: (
            <span className="text-red text-nowrap">
              <NumberCell
                value={liquidationLTV ? liquidationLTV * 100 : 0}
                options={{
                  maximumFractionDigitsGreater1: 2,
                  maximumFractionDigitsLess1: 2,
                  minimumFractionDigitsGreater1: 2,
                  minimumFractionDigitsLess1: 2
                }}
              />
              %
            </span>
          )
        }
      ]
    }
  ]
}

async function calculateMarginTradePredictionData(
  market: Market,
  inputAmount: bigint,
  leverage: number,
  collateralType: string,
  debtType: string,
  operationType: 'long' | 'short',
  selectedToken?: Token,
  token0?: Token,
  token1?: Token
): Promise<PredictionResult> {
  try {
    const normalizedCollateralType = collateralType.replace(/^0x/, '')
    const normalizedDebtType = debtType.replace(/^0x/, '')

    const collateralConfig = market.getAssetConfiguration(
      normalizedCollateralType
    )
    const debtConfig = market.getAssetConfiguration(normalizedDebtType)

    // console.log('Collateral Config:', collateralConfig)
    // console.log('Debt Config:', debtConfig)

    if (!collateralConfig) {
      throw new Error(
        `Collateral asset ${normalizedCollateralType} not found in market`
      )
    }

    if (!debtConfig) {
      throw new Error(`Debt asset ${normalizedDebtType} not found in market`)
    }

    if (!collateralConfig.collateralSetting()) {
      throw new Error(
        `Asset ${normalizedCollateralType} is not configured as collateral`
      )
    }

    if (!debtConfig.borrowSetting()) {
      throw new Error(
        `Asset ${normalizedDebtType} is not configured for borrowing`
      )
    }

    const collateralPrice = market.getDeposit(normalizedCollateralType).price()
    const debtPrice = market.getBorrow(normalizedDebtType).price()

    // console.log('Collateral Price:', collateralPrice.toString())
    // console.log('Debt Price:', debtPrice.toString())

    if (collateralPrice.isZero()) {
      throw new Error(
        `Collateral price for ${normalizedCollateralType} is zero`
      )
    }

    if (debtPrice.isZero()) {
      throw new Error(`Debt price for ${normalizedDebtType} is zero`)
    }

    // 根据 selectedToken 确定实际的 inputAmount
    let actualInputAmount: bigint
    let inputCoinType: string

    if (
      selectedToken?.coinType.replace(/^0x/, '').toLowerCase() ===
      normalizedCollateralType.toLowerCase()
    ) {
      // 如果选择的是抵押品token，直接使用
      actualInputAmount = inputAmount
      inputCoinType = normalizedCollateralType
    } else if (
      selectedToken?.coinType.replace(/^0x/, '').toLowerCase() ===
      normalizedDebtType.toLowerCase()
    ) {
      // 如果选择的是债务token，需要转换为抵押品token的数量
      const debtValueUSD = debtPrice.mul(Decimal.fromBigInt(inputAmount))
      const collateralAmount = debtValueUSD
        .div(collateralPrice.asBigInt())
        .asBigInt()
      actualInputAmount = collateralAmount
      inputCoinType = normalizedCollateralType // 杠杆操作总是以抵押品token为输入
    } else {
      // 默认情况
      actualInputAmount = inputAmount
      inputCoinType = normalizedCollateralType
    }

    let leverageOperation
    try {
      leverageOperation =
        operationType === 'long'
          ? LeverageOperation.long(
              market,
              Decimal.fromNumber(0.99), // targetLTV
              inputCoinType, // inputCoinType - 使用转换后的类型
              actualInputAmount, // 使用转换后的数量
              Decimal.fromNumber(leverage),
              normalizedCollateralType, // leftCoinType (collateral)
              normalizedDebtType, // rightCoinType (debt)
              Decimal.fromNumber(0.005) // swapSlippage
            )
          : LeverageOperation.short(
              market,
              Decimal.fromNumber(0.99), // targetLTV
              inputCoinType, // inputCoinType - 使用转换后的类型
              actualInputAmount, // 使用转换后的数量
              Decimal.fromNumber(leverage),
              normalizedCollateralType, // leftCoinType
              normalizedDebtType, // rightCoinType
              Decimal.fromNumber(0.005) // swapSlippage
            )
    } catch (error) {
      console.error('Failed to create leverage operation:', error)
      throw error
    }

    // 2. 创建预估的杠杆仓位
    const estimatedPosition = LeverageObligation.from(leverageOperation, market)
    const lendingObligation = estimatedPosition.lendingMarketObligation

    // 3. 获取市场配置信息
    const maxLTV =
      collateralConfig?.collateralSetting()?.collateralFactor.asNumber() || 0

    // 4. 获取小数位数
    const collateralDecimals =
      market.coinMetadatas.get(normalizedCollateralType)?.decimals || 9
    const debtDecimals =
      market.coinMetadatas.get(normalizedDebtType)?.decimals || 6

    const currentLTV = lendingObligation.currentLTV(market).asNumber()
    const netValue = lendingObligation.netValue().asNumber()

    // 获取借贷和存款资产信息
    let collateralDeposit, debtBorrow

    try {
      collateralDeposit = lendingObligation.getDeposit(normalizedCollateralType)
    } catch {
      // 如果找不到存款，使用预估的数量
      const estimatedCollateralAmount =
        (inputAmount * BigInt(Math.floor(leverage * 1000))) / 1000n
      collateralDeposit = {
        amount: () => estimatedCollateralAmount,
        price: () => collateralPrice
      }
    }

    try {
      debtBorrow = lendingObligation.getBorrow(normalizedDebtType)
    } catch {
      // 如果找不到借贷，使用预估的数量
      const estimatedDebtAmount =
        (inputAmount * BigInt(Math.floor(leverage * 1000))) / 1000n
      debtBorrow = {
        amount: () => estimatedDebtAmount,
        price: () => debtPrice
      }
    }

    // 计算清算价格
    const baseLiquidationPrice = estimatedPosition
      .collateralLiquidationPrice(market)
      .asNumber()
    let liquidationPrice: number
    let currentPrice: number
    let priceChangePercent: number

    if (
      selectedToken?.coinType.replace(/^0x/, '').toLowerCase() ===
      normalizedCollateralType.toLowerCase()
    ) {
      // 如果选择的是抵押品token，使用抵押品价格
      currentPrice = collateralPrice.asNumber()
      liquidationPrice = baseLiquidationPrice
      priceChangePercent = new BigNumber(liquidationPrice)
        .minus(currentPrice)
        .dividedBy(currentPrice)
        .multipliedBy(100)
        .toNumber()
    } else if (
      selectedToken?.coinType.replace(/^0x/, '').toLowerCase() ===
      normalizedDebtType.toLowerCase()
    ) {
      // 如果选择的是债务token，使用债务价格
      currentPrice = debtPrice.asNumber()
      liquidationPrice = new BigNumber(baseLiquidationPrice)
        .multipliedBy(currentPrice)
        .dividedBy(collateralPrice.asNumber())
        .toNumber()
      priceChangePercent = new BigNumber(liquidationPrice)
        .minus(currentPrice)
        .dividedBy(currentPrice)
        .multipliedBy(100)
        .toNumber()
    } else {
      // 默认使用抵押品价格
      currentPrice = collateralPrice.asNumber()
      liquidationPrice = baseLiquidationPrice
      priceChangePercent = new BigNumber(liquidationPrice)
        .minus(currentPrice)
        .dividedBy(currentPrice)
        .multipliedBy(100)
        .toNumber()
    }

    // 5. 根据selectedToken和token类型确定符号
    const collateralTokenSymbol = getTokenSymbol(
      normalizedCollateralType,
      selectedToken,
      token0,
      token1
    )
    const debtTokenSymbol = getTokenSymbol(
      normalizedDebtType,
      selectedToken,
      token0,
      token1
    )

    return {
      leverageValue: leverage,
      liquidationPrice,
      priceChangePercent,
      netValue,
      collateralDeposit,
      debtBorrow,
      collateralDecimals,
      debtDecimals,
      currentLTV,
      maxLTV,
      collateralTokenSymbol,
      debtTokenSymbol,
      collateralTokenType: normalizedCollateralType,
      debtTokenType: normalizedDebtType
    }
  } catch (error) {
    console.error('Error in calculateMarginTradePredictionData:', error)
    console.error('Error details:', {
      collateralType,
      debtType,
      inputAmount: inputAmount.toString(),
      leverage,
      operationType
    })
    throw error
  }
}

// 辅助函数：根据token类型和selectedToken获取对应的符号
function getTokenSymbol(
  tokenType: string,
  selectedToken?: Token,
  token0?: Token,
  token1?: Token
): string {
  if (!selectedToken || !token0 || !token1) {
    // 如果没有完整的token信息，使用默认逻辑
    const parts = tokenType.split('::')
    return parts[parts.length - 1] || 'Unknown'
  }

  // 标准化地址进行比较
  const normalizeSuiAddress = (address: string) => {
    return address.replace(/^0x/, '').toLowerCase()
  }

  const selectedTokenAddress = normalizeSuiAddress(selectedToken.coinType)
  const leftCoinAddress = normalizeSuiAddress(token0.coinType)
  const rightCoinAddress = normalizeSuiAddress(token1.coinType)
  const currentTokenAddress = normalizeSuiAddress(tokenType)

  // 判断当前tokenType对应哪个token
  if (currentTokenAddress === selectedTokenAddress) {
    return selectedToken.symbol
  } else if (currentTokenAddress === leftCoinAddress) {
    return token0.symbol
  } else if (currentTokenAddress === rightCoinAddress) {
    return token1.symbol
  }

  // 默认返回
  const parts = tokenType.split('::')
  return parts[parts.length - 1] || 'Unknown'
}
