import { usePebbleSDK } from '@/hooks/use-pebble-sdk'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useContractMutation } from '@/hooks/use-contract-mutation'
import { ContractError } from '@/types/error'
import { getMarket, prepareCoinsForAmount } from '@pebble-protocol/pebble-sdk'
import { Transaction } from '@mysten/sui/transactions'
import { parseUnits } from '@/lib/units'
import { useAllBalance } from '@/hooks/use-all-balance'
import { NETWORK } from '@/config/networks'
import { useLeverageClient } from './use-leverage-client'
import { LeverageTransactionType } from '../types'
import { useLeveragePosition } from './use-leverage-position'

interface ILeverageTransactionParams {
  operationType: LeverageTransactionType
  amount: string
  coinType: string
  decimals: number
}

export const useLeverageTransaction = (
  marketName: string,
  ownerCapId: string
) => {
  const { userAddress } = usePebbleWallet()
  const { pebbleClient, walletAdapter } = usePebbleSDK()
  const leverageClient = useLeverageClient()
  const { refetch: refetchAllBalance } = useAllBalance()
  const { refetch: refetchLeveragePosition } = useLeveragePosition()

  const mutationFn = async (params: ILeverageTransactionParams) => {
    if (!leverageClient) {
      throw new ContractError('❌ Failed to fetch obligationInfo')
    }
    const { operationType, amount, coinType } = params
    console.log('OperationParams', operationType, amount, coinType)

    // Get the MainMarket from configuration
    const market = getMarket(NETWORK, marketName.toString())
    const marketId = market.objectId
    const marketType = market.type

    let confirmed
    const amountBigInt = parseUnits(String(amount), params.decimals)
    try {
      // Transaction mode: build and execute transaction
      const tx = new Transaction()
      // if (isSuiCoinType(coinType)) {
      //   // Build a dummy transaction to estimate gas
      //   const dummyTx = new Transaction()
      //   const dummyCoin = dummyTx.splitCoins(dummyTx.gas, [amountBigInt])
      //   dummyTx.setSender(userAddress)
      //   config.populateTransactionFn(
      //     dummyTx,
      //     marketId,
      //     marketType,
      //     obligationOwnerCapId,
      //     coinType,
      //     dummyCoin
      //   )
      //   // Estimate gas and set budget
      //   const estimatedGas = await estimateGasForTransaction(
      //     dummyTx,
      //     pebbleClient.provider
      //   )
      //   tx.setGasBudget(amountBigInt + estimatedGas)
      //   console.log(
      //     `Gas budget set to: ${amountBigInt + estimatedGas} (supply/repay: ${amountBigInt}, gas: ${estimatedGas})`
      //   )
      // }
      const coinObjectId = await prepareCoinsForAmount(
        tx,
        pebbleClient.provider,
        userAddress,
        coinType,
        amountBigInt
      )
      if (operationType === LeverageTransactionType.Repay) {
        leverageClient.populateRepayTransaction(
          tx,
          marketId,
          marketType,
          ownerCapId,
          coinType,
          coinObjectId as string,
          walletAdapter
        )
      } else if (operationType === LeverageTransactionType.Deposit) {
        leverageClient.populateDepositTransaction(
          tx,
          marketId,
          marketType,
          ownerCapId,
          coinType,
          coinObjectId as string
        )
      }
      confirmed = await walletAdapter.signAndExecuteTransaction(tx)
    } catch (error) {
      console.error(error)
      console.log(
        'coinType',
        coinType,
        'amountBigInt',
        amountBigInt,
        'decimals',
        params.decimals
      )
      throw new ContractError('Failed to execute operation')
    }

    if (confirmed.effects?.status.status === 'failure') {
      throw new ContractError('', confirmed.digest)
    }

    refetchAllBalance()
    refetchLeveragePosition()
    return confirmed
  }

  return {
    operation: useContractMutation({
      mutationKey: [marketName, ownerCapId, userAddress],
      mutationFn: mutationFn
    })
  }
}
