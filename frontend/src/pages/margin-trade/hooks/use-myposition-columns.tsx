import type { ColumnDef } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { WebIcon } from '@/assets/icons'
import { getIconUrl } from '@/lib/assets'
import { Avatar } from '@/components/ui/avatar'
import { NumberCell } from '@/components/number-cell'
import useMarketTokenInfo from '@/pages/market/hooks/use-market-token-info'
import { Badge } from '@/components/ui/badge'

interface IPosition {
  id: string
  marketName: string
  leftAsset: string
  rightAsset: string
  leverage: number
  leverageType: string
  size: string
  pnl1: string
  pnl2: string
  entryPrice: string
  collateralLiqPrice: string
  debtLiqPrice: string
  hash: string
}

// eslint-disable-next-line react-refresh/only-export-components
const AssetCell = ({ position }: { position: IPosition }) => {
  const { marketTokenInfo: leftMarketTokenInfo } = useMarketTokenInfo(
    position.marketName,
    position.leftAsset
  )

  const { marketTokenInfo: rightMarketTokenInfo } = useMarketTokenInfo(
    position.marketName,
    position.rightAsset
  )

  return (
    <div className="flex items-center gap-x-1.5">
      <div className="flex items-center">
        <div className="relative mr-4">
          <Avatar
            fallback={leftMarketTokenInfo?.tokenInfo.symbol.slice(0, 1)}
            src={getIconUrl(leftMarketTokenInfo?.tokenInfo.symbol ?? '')}
            alt="left-asset-logo"
            size={18}
          />
          <Avatar
            fallback={rightMarketTokenInfo?.tokenInfo.symbol.slice(0, 1)}
            src={getIconUrl(rightMarketTokenInfo?.tokenInfo.symbol ?? '')}
            alt="right-asset-logo"
            size={18}
            className="absolute left-3 top-0"
          />
        </div>
        <span className="text-sm">
          {leftMarketTokenInfo?.tokenInfo.symbol}/
          {rightMarketTokenInfo?.tokenInfo.symbol}
        </span>
      </div>
      <Badge
        className="w-fit px-1.5 py-1 text-xs"
        variant={position.leverageType === 'Short' ? 'red' : 'green'}>
        <NumberCell
          value={position.leverage}
          options={{
            maximumFractionDigitsGreater1: 1,
            minimumFractionDigitsGreater1: 1,
            maximumFractionDigitsLess1: 1,
            minimumFractionDigitsLess1: 1
          }}
        />
        x {position.leverageType}
      </Badge>
    </div>
  )
}

AssetCell.displayName = 'AssetCell'

export const useMyPositionColumns = () => {
  const columns: ColumnDef<IPosition>[] = [
    {
      id: 'position',
      header: 'Position',
      accessorKey: 'id',
      cell: ({ row }) => {
        const position = row.original
        return <AssetCell position={position} />
      }
    },
    {
      id: 'size',
      header: 'Size',
      accessorKey: 'size',
      size: 120
    },
    {
      id: 'pnl',
      header: 'PnL',
      accessorKey: 'pnl',
      cell: ({ row }) => {
        const position = row.original
        return (
          <div className="flex flex-col items-start gap-y-1 text-red">
            <div className="text-sm">{position.pnl1}</div>
            <div className="text-[10px]">{position.pnl2}</div>
          </div>
        )
      },
      size: 120
    },
    {
      id: 'entryPrice',
      header: 'Entry Price',
      accessorKey: 'entryPrice',
      cell: ({ row }) => {
        const position = row.original
        return <NumberCell value={position.entryPrice} />
      },
      size: 120
    },
    {
      id: 'collateralLiqPrice',
      header: 'Collateral Liq. Price',
      accessorKey: 'collateralLiqPrice',
      cell: ({ row }) => {
        const position = row.original
        return <NumberCell value={position.collateralLiqPrice} />
      },
      size: 120
    },
    {
      id: 'debtLiqPrice',
      header: 'Debt Liq. Price',
      accessorKey: 'debtLiqPrice',
      cell: ({ row }) => {
        const position = row.original
        return <NumberCell value={position.debtLiqPrice} />
      },
      size: 120
    },
    {
      id: 'hash',
      header: () => null,
      accessorKey: 'hash',
      size: 14,
      enableSorting: false,
      cell: ({ row }) => {
        const hash = row.original.hash
        return (
          <div className="flex items-center justify-end">
            <Button
              onClick={() => {
                window.open(hash, '_blank')
              }}
              variant="icon"
              className="h-[14px] w-[14px]">
              <WebIcon className="size-[14px]" />
            </Button>
          </div>
        )
      }
    }
  ]

  return {
    columns
  }
}
