import { usePebbleSDK } from '@/hooks/use-pebble-sdk'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useContractMutation } from '@/hooks/use-contract-mutation'
import { ContractError } from '@/types/error'
import { Decimal, LeverageObligation } from '@pebble-protocol/pebble-sdk'
import { Transaction } from '@mysten/sui/transactions'
import { useAllBalance } from '@/hooks/use-all-balance'
import { useLeverageClient } from './use-leverage-client'
import { useLeveragePosition } from './use-leverage-position'
import { useMarketInfo } from '@/hooks/use-market'

interface ILeverageAdjustParams {
  leverageObligation: LeverageObligation
  newLeverage: number
  swapSlippage: number
}

export const useLeverageAdjust = (marketName: string, ownerCapId: string) => {
  const { userAddress } = usePebbleWallet()
  const { walletAdapter } = usePebbleSDK()
  const leverageClient = useLeverageClient()
  const { refetch: refetchAllBalance } = useAllBalance()
  const { refetch: refetchLeveragePosition } = useLeveragePosition()
  const marketInfo = useMarketInfo(marketName)

  const mutationFn = async (params: ILeverageAdjustParams) => {
    if (!leverageClient || !marketInfo) {
      throw new ContractError('❌ Failed to fetch obligationInfo')
    }
    const { leverageObligation, newLeverage, swapSlippage } = params
    console.log(
      'useLeverageAdjust',
      leverageObligation,
      newLeverage,
      swapSlippage
    )

    let confirmed
    try {
      // Transaction mode: build and execute transaction
      const tx = new Transaction()
      const currentLeverage = leverageObligation.leverage()
      console.log('currentLeverage', currentLeverage.asNumber())
      if (currentLeverage.asNumber() === newLeverage) {
        throw new ContractError('New leverage is the same as current leverage')
      }
      if (newLeverage > currentLeverage.asNumber()) {
        await leverageClient.increaseLeverage(
          tx,
          marketInfo.market,
          ownerCapId,
          leverageObligation,
          Decimal.fromNumber(newLeverage),
          swapSlippage,
          walletAdapter
        )
      } else {
        await leverageClient.reduceLeverage(
          tx,
          marketInfo.market,
          ownerCapId,
          leverageObligation,
          Decimal.fromNumber(newLeverage),
          swapSlippage,
          walletAdapter
        )
      }
      confirmed = await walletAdapter.signAndExecuteTransaction(tx)
    } catch (error) {
      console.error(error)
      throw new ContractError('Failed to execute operation')
    }

    if (confirmed.effects?.status.status === 'failure') {
      throw new ContractError('', confirmed.digest)
    }

    refetchAllBalance()
    refetchLeveragePosition()
    return confirmed
  }

  return {
    operation: useContractMutation({
      mutationKey: ['leverage-adjust', marketName, ownerCapId, userAddress],
      mutationFn: mutationFn
    })
  }
}
