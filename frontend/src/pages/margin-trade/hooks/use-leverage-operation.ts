import { useCallback } from 'react'
import { useLeverageClient } from './use-leverage-client'
import { Decimal, LeverageOperation, Market } from '@pebble-protocol/pebble-sdk'
import { LeverageType } from './use-leverage'
import { useQuery } from '@tanstack/react-query'

export interface ILeverageOperationParams {
  market: Market
  amount: bigint
  leverage: number
  targetLTV?: number
  swapSlippage?: number
  leverageType: LeverageType
  leftCoinType: string
  inputCoinType: string
  rightCoinType: string
}

export const useLeverageOperation = () => {
  const leverageClient = useLeverageClient()

  const createLeverageOperation = useCallback(
    async (params: ILeverageOperationParams): Promise<LeverageOperation> => {
      if (!leverageClient) {
        throw new Error('❌ LeverageClient not available')
      }

      const {
        market,
        amount,
        leverage,
        targetLTV = 0.99, //Todo: check
        swapSlippage = 0.005,
        leverageType,
        leftCoinType,
        inputCoinType,
        rightCoinType
      } = params

      // Create leverage operation based on type
      if (leverageType === LeverageType.Long) {
        return LeverageOperation.long(
          market,
          Decimal.fromNumber(targetLTV),
          inputCoinType, // input coin type
          amount,
          Decimal.fromNumber(leverage),
          leftCoinType, // left coin type
          rightCoinType, // right coin type
          Decimal.fromNumber(swapSlippage)
        )
      } else {
        return LeverageOperation.short(
          market,
          Decimal.fromNumber(targetLTV),
          inputCoinType, // input coin type
          amount,
          Decimal.fromNumber(leverage),
          leftCoinType, // left coin type
          rightCoinType, // right coin type
          Decimal.fromNumber(swapSlippage)
        )
      }
    },
    [leverageClient]
  )

  return {
    createLeverageOperation
  }
}

export const useLeverageOperationQuery = (
  params?: ILeverageOperationParams,
  options?: { enabled?: boolean }
) => {
  const { createLeverageOperation } = useLeverageOperation()

  return useQuery({
    queryKey: [
      'leverageOperation',
      params?.amount?.toString(),
      params?.leverageType,
      params?.targetLTV,
      params?.swapSlippage,
      params?.leftCoinType,
      params?.inputCoinType,
      params?.rightCoinType
    ],
    queryFn: async () => {
      if (!params) return null
      return await createLeverageOperation(params)
    },
    enabled: !!params && options?.enabled !== false
  })
}
