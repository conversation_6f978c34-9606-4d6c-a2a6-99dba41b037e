import { Button } from '@/components/ui/button'
import { useMarginTradePrediction } from '../hooks/use-noposition-prediction'
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useState, useMemo, useEffect, useCallback } from 'react'
import { Slider } from '@/components/ui/slider'
import { Avatar } from '@/components/ui/avatar'
import BigNumber from 'bignumber.js'
import { normalizeSuiAddress } from '@mysten/sui/utils'
import { BalanceInput, type Token } from '@/components/balance-input'
import { LEVERAGE_MIN_MULTIPLIER, MarginTradeDepositTabEnum } from '../types'
import { DepositCollapsibleItem } from '@/components/deposit-collapsible-item'
import { getIconUrl } from '@/lib/assets'
import { useLeverage, LeverageType } from '../hooks/use-leverage'
import { useLeverageOperationQuery } from '../hooks/use-leverage-operation'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { parseUnits } from '@/lib/units'
import { useLeverageMarket } from '../hooks/use-leverage-market'
import type { IMarginTradeItem } from '@/types/margin-trade'
import { NumberCell } from '@/components/number-cell'
import { SlippageSetting } from '@/components/dialogs/slippage-setting'

export const NoPositionDeposit = ({
  tradeItem,
  selectedToken,
  setSelectedToken,
  onConfirm
}: {
  tradeItem?: IMarginTradeItem
  selectedToken: Token | undefined
  setSelectedToken: (token: Token) => void
  onConfirm: () => void
}) => {
  const { userAddress } = usePebbleWallet()
  const {
    execute: executeLeverage,
    isLoading: isLeverageLoading,
    error: leverageError
  } = useLeverage()
  const { data: leverageMarket } = useLeverageMarket()
  const [balanceInputValue, setBalanceInputValue] = useState<string>()
  const [sizeValue, setSizeValue] = useState<number>()
  const [multiplier, setMultiplier] = useState<number>(LEVERAGE_MIN_MULTIPLIER)
  const [tab, setTab] = useState<MarginTradeDepositTabEnum>(
    MarginTradeDepositTabEnum.Buy
  )
  const [slippage, setSlippage] = useState<number>(0.01)
  // const marketData = useMarketInfo(tradeItem?.fromMarket)

  const tokens = useMemo(() => {
    if (!tradeItem) return undefined
    return {
      token0: {
        symbol: tradeItem.tokenInfo0.symbol,
        icon: getIconUrl('sui'),
        coinType: `0x${tradeItem.token0}`
      },
      token1: {
        symbol: tradeItem.tokenInfo1.symbol,
        icon: getIconUrl('usdc', 'svg'),
        coinType: `0x${tradeItem.token1}`
      }
    }
  }, [tradeItem])

  const { predictionData } = useMarginTradePrediction(
    tradeItem?.fromMarket || '',
    parseUnits(balanceInputValue?.toString() || '0'),
    multiplier,
    tokens?.token0?.coinType,
    tokens?.token1?.coinType,
    tab === MarginTradeDepositTabEnum.Buy ? 'long' : 'short',
    selectedToken,
    tokens?.token0,
    tokens?.token1
  )

  useEffect(() => {
    if (tokens?.token0) {
      setSelectedToken(tokens.token0)
    }
  }, [tokens, setSelectedToken])

  useEffect(() => {
    if (slippage === 0 && tradeItem) {
      setSlippage(
        tab === MarginTradeDepositTabEnum.Buy
          ? tradeItem.slippageLeftToRight
          : tradeItem.slippageRightToLeft
      )
    }
  }, [slippage, tab, tradeItem])

  const leftCoinType = useMemo(() => {
    if (!tradeItem) return undefined
    return tradeItem.tokenInfo0
  }, [tradeItem])

  const inputCoinType = useMemo(() => {
    if (!selectedToken || !tradeItem) return undefined
    return tradeItem?.token0 === selectedToken.coinType.substring(2)
      ? tradeItem.tokenInfo0
      : tradeItem.tokenInfo1
  }, [selectedToken, tradeItem])

  const rightCoinType = useMemo(() => {
    if (!tradeItem) return undefined
    return tradeItem.tokenInfo1
  }, [tradeItem])

  const leverageParams = useMemo(() => {
    if (
      !leverageMarket ||
      !leftCoinType ||
      !inputCoinType ||
      !rightCoinType ||
      !tradeItem
    )
      return undefined
    const leverageType =
      tab === MarginTradeDepositTabEnum.Buy
        ? LeverageType.Long
        : LeverageType.Short
    return {
      market: leverageMarket,
      targetLTV:
        tab === MarginTradeDepositTabEnum.Buy
          ? tradeItem?.longMaxLTV
          : tradeItem?.shortMaxLTV,
      swapSlippage: slippage,
      leverageType,
      leftCoinType: leftCoinType.address,
      inputCoinType: inputCoinType.address,
      rightCoinType: rightCoinType.address
    }
  }, [
    inputCoinType,
    leftCoinType,
    leverageMarket,
    rightCoinType,
    slippage,
    tab,
    tradeItem
  ])

  // Prepare common leverage parameters
  const operationParams = useMemo(() => {
    if (!balanceInputValue || !userAddress || !leverageParams || !inputCoinType)
      return undefined

    const amount = parseUnits(
      String(balanceInputValue),
      Number(inputCoinType.decimals)
    )

    return { ...leverageParams, amount, leverage: multiplier }
  }, [
    balanceInputValue,
    userAddress,
    multiplier,
    leverageParams,
    inputCoinType
  ])

  // const { data: maxLeverageData, isLoading: isMaxLeverageLoading } =
  //   useMaxLeverage(leverageParams)

  const {
    data: leverageOperation,
    isLoading: isLeverageOperationLoading,
    error: leverageOperationError
  } = useLeverageOperationQuery(operationParams)

  const maxLeverage = useMemo(() => {
    if (!leverageOperation) return LEVERAGE_MIN_MULTIPLIER
    // console.log('maxLeverage', leverageOperation.maxLeverage.asNumber())
    return leverageOperation.maxLeverage.asNumber()
  }, [leverageOperation])

  const handleBuy = async () => {
    if (!operationParams || !leverageOperation) return

    try {
      const txHash = await executeLeverage({
        leverageOperation,
        amount: operationParams.amount,
        leverage: operationParams.leverage,
        swapSlippage: operationParams.swapSlippage,
        leverageType: operationParams.leverageType,
        inputCoinType: operationParams.inputCoinType
      })
      console.log('Long position opened successfully:', txHash)
      onConfirm()
    } catch (error) {
      console.error('Error opening long position:', error)
    }
  }

  const handleSell = async () => {
    if (!operationParams || !leverageOperation) return

    try {
      const txHash = await executeLeverage({
        leverageOperation,
        amount: operationParams.amount,
        leverage: operationParams.leverage,
        swapSlippage: operationParams.swapSlippage,
        leverageType: operationParams.leverageType,
        inputCoinType: operationParams.inputCoinType
      })
      console.log('Short position opened successfully:', txHash)
      onConfirm()
    } catch (error) {
      console.error('Error opening short position:', error)
    }
  }

  const handInputChange = useCallback(
    (balanceInput: string | undefined, leverageInput: number) => {
      if (!selectedToken) return

      const selectedTokenAddress = normalizeSuiAddress(selectedToken.coinType)
      const leftCoinAddress = !leftCoinType?.address
        ? null
        : normalizeSuiAddress(leftCoinType?.address)
      const rightCoinAddress = !rightCoinType?.address
        ? null
        : normalizeSuiAddress(rightCoinType.address)

      const isLeftCoin =
        !!leftCoinAddress &&
        selectedTokenAddress.toLowerCase() === leftCoinAddress.toLowerCase()

      const isRightCoin =
        !!rightCoinAddress &&
        selectedTokenAddress.toLowerCase() === rightCoinAddress.toLowerCase()

      if (!isLeftCoin && !isRightCoin) return

      if (isLeftCoin) {
        setSizeValue(
          BigNumber(balanceInput || 0)
            .multipliedBy(leverageInput)
            .toNumber()
        )
      } else if (rightCoinType?.price && leftCoinType?.price) {
        const value = BigNumber(balanceInput || 0)
          .multipliedBy(rightCoinType.price)
          .div(leftCoinType.price)
          .multipliedBy(leverageInput)
          .toNumber()
        setSizeValue(value)
      }
    },
    [leftCoinType?.address, leftCoinType?.price, rightCoinType, selectedToken]
  )

  const renderLeverageSettings = () => (
    <div className="flex flex-col">
      <div className="mb-5">Leverage</div>
      <Slider
        value={[multiplier ?? 0]}
        onValueChange={(value) => {
          setMultiplier(value[0])
          handInputChange(balanceInputValue, value[0])
        }}
        min={LEVERAGE_MIN_MULTIPLIER}
        max={maxLeverage}
        step={0.1}
        className="w-full"
        disabled={isLeverageOperationLoading}
      />
      <div className="mt-3 text-xs flex items-center justify-between">
        <span>{LEVERAGE_MIN_MULTIPLIER}x</span>
        <span>
          {isLeverageOperationLoading
            ? 'Loading...'
            : `${maxLeverage.toFixed(1)}x`}
        </span>
      </div>
      {leverageOperationError && (
        <div className="text-red-500 text-xs mt-1">
          Error calculating max leverage: {leverageOperationError.message}
        </div>
      )}
    </div>
  )

  const renderPrediction = () => {
    if (
      !balanceInputValue ||
      isNaN(Number(balanceInputValue)) ||
      !multiplier ||
      isNaN(Number(multiplier))
    )
      return null

    return (
      <div className="flex flex-col gap-y-3 text-xs">
        <span className="flex items-center justify-between">
          <span className="opacity-60">Transaction Settings</span>
          <SlippageSetting slippage={slippage} onConfirm={setSlippage} />
        </span>
        {predictionData?.map((item) => (
          <DepositCollapsibleItem key={item.id} data={item} />
        ))}
      </div>
    )
  }
  return (
    <div className="border border-border-8 pt-0 rounded-[10px] w-[377px] h-full flex flex-col gap-y-6 bg-deposit-gradient">
      <div className="px-6">
        <Tabs
          value={tab}
          onValueChange={(item) => {
            setTab(item as MarginTradeDepositTabEnum)
            setBalanceInputValue(undefined)
            setSizeValue(undefined)
            setMultiplier(LEVERAGE_MIN_MULTIPLIER)
          }}
          orientation="vertical">
          <TabsList
            className="pl-0 w-full grid grid-cols-2"
            aria-label="deposit tabs">
            <TabsTrigger value={MarginTradeDepositTabEnum.Buy}>
              {MarginTradeDepositTabEnum.Buy}
            </TabsTrigger>
            <TabsTrigger value={MarginTradeDepositTabEnum.Sell}>
              {MarginTradeDepositTabEnum.Sell}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div className="px-6 pb-6 flex flex-col gap-y-6">
        {tokens && (
          <BalanceInput
            value={balanceInputValue}
            onChange={(value) => {
              setBalanceInputValue(value)
              handInputChange(value, multiplier)
            }}
            tokens={tokens}
            selectedToken={selectedToken}
            onSelectedTokenChange={(v) => {
              setBalanceInputValue(undefined)
              setSelectedToken(v)
            }}
            price={
              inputCoinType?.price ? Number(inputCoinType.price) : undefined
            }
            title="Deposit asset"
          />
        )}
        <div className="border flex items-center justify-between border-border-8 bg-border-2 rounded-xl p-3">
          <div className="flex flex-col gap-y-3">
            <div className="text-xs opacity-60">
              {tab === MarginTradeDepositTabEnum.Buy ? 'Long' : 'Short'} Size
            </div>
            <div className="flex items-center gap-x-1.5">
              <Avatar
                src={getIconUrl('sui')}
                fallback="SUI"
                alt="sui-logo"
                size={24}
              />
              <span className="text-2xl">{leftCoinType?.symbol}</span>
            </div>
          </div>
          <NumberCell className="text-2xl opacity-40" value={sizeValue} />
        </div>
        {renderLeverageSettings()}
        {tab === MarginTradeDepositTabEnum.Buy ? (
          <>
            {leverageError && (
              <div className="text-red-500 text-sm">Error: {leverageError}</div>
            )}
            <Button
              variant="secondary"
              onClick={handleBuy}
              disabled={
                isLeverageLoading || !leverageParams || !leverageOperation
              }>
              {isLeverageLoading ? 'Opening Long Position...' : 'Buy'}
            </Button>
          </>
        ) : (
          <>
            {leverageError && (
              <div className="text-red-500 text-sm">Error: {leverageError}</div>
            )}
            <Button
              variant="secondary"
              onClick={handleSell}
              disabled={
                isLeverageLoading || !leverageParams || !leverageOperation
              }>
              {isLeverageLoading ? 'Opening Short Position...' : 'Sell'}
            </Button>
          </>
        )}
        {renderPrediction()}
      </div>
    </div>
  )
}
