import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useState, useMemo } from 'react'
import { BalanceInput } from '@/components/balance-input'
import { ManageSizeTabEnum } from '../types'
import { getIconUrl } from '@/lib/assets'
import { useLeverage, LeverageType } from '../hooks/use-leverage'
import { useLeverageOperationQuery } from '../hooks/use-leverage-operation'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { parseUnits } from '@/lib/units'
import type { IMarginTradeItem } from '@/types/margin-trade'
import type { LeverageObligation } from '@pebble-protocol/pebble-sdk'
import { useMarketInfo } from '@/hooks/use-market'
import { SlippageSetting } from '@/components/dialogs/slippage-setting'
import { useLeverageDecrease } from '../hooks/use-leverage-decrease'
import { TransactionDetail } from './transaction-detail'

export const ManageSize = ({
  marketName,
  ownerCapId,
  position,
  tradeItem
}: {
  marketName: string
  ownerCapId: string
  position: LeverageObligation
  tradeItem?: IMarginTradeItem
}) => {
  const { userAddress } = usePebbleWallet()
  const [tab, setTab] = useState<ManageSizeTabEnum>(ManageSizeTabEnum.Increase)
  const { execute: executeLeverage, isLoading: isLeverageLoading } =
    useLeverage()
  const { operation: leverageDecrease } = useLeverageDecrease(
    marketName,
    ownerCapId
  )
  const marketInfo = useMarketInfo(marketName)
  const [balanceInputValue, setBalanceInputValue] = useState<string>()
  const [slippage, setSlippage] = useState<number>(0.01)

  const tokenAddress = useMemo(() => {
    if (tab === ManageSizeTabEnum.Increase) {
      return position.principleCoinType(tradeItem?.tokenInfo0.address ?? '')
    } else {
      return position.leverageObligation.info?.borrow ?? ''
    }
  }, [position, tradeItem, tab])

  const inputCoinType = useMemo(() => {
    if (!tradeItem) return undefined
    return tradeItem?.token0 === tokenAddress
      ? tradeItem.tokenInfo0
      : tradeItem.tokenInfo1
  }, [tokenAddress, tradeItem])

  const tokens = useMemo(() => {
    if (!inputCoinType) return undefined
    return {
      token0: {
        symbol: inputCoinType.symbol,
        icon: getIconUrl('sui'),
        coinType: `0x${inputCoinType.address}`
      }
    }
  }, [inputCoinType])

  const leverageParams = useMemo(() => {
    if (!marketInfo || !position || !tradeItem) return undefined
    return {
      market: marketInfo.market,
      targetLTV: tradeItem?.longMaxLTV,
      swapSlippage: slippage,
      leverageType: LeverageType.Long,
      inputCoinType: tokenAddress,
      leftCoinType: tradeItem?.token0 ?? '',
      rightCoinType: tradeItem?.token1 ?? ''
    }
  }, [position, tokenAddress, marketInfo, tradeItem, slippage])

  const maxWithdrawAmount = useMemo(() => {
    // return Number(
    //   formatUnits(position.principleAmount(), Number(inputCoinType?.decimals))
    // )
    if (!marketInfo?.market) return 0
    return Number(
      (
        position.netValueUSD(marketInfo?.market).asNumber() /
        Number(inputCoinType?.price)
      ).toFixed(6)
    )
  }, [position, inputCoinType, marketInfo])

  // Prepare common leverage parameters
  const operationParams = useMemo(() => {
    if (!balanceInputValue || !userAddress || !leverageParams || !inputCoinType)
      return undefined

    const amount = parseUnits(
      String(balanceInputValue),
      Number(inputCoinType.decimals)
    )

    return {
      ...leverageParams,
      amount,
      leverage: position.leverage().asNumber()
    }
  }, [balanceInputValue, userAddress, position, leverageParams, inputCoinType])

  const { data: leverageOperation, isLoading: isOperationLoading } =
    useLeverageOperationQuery(operationParams)

  const isLoading = isLeverageLoading || isOperationLoading

  const handleIncrease = async () => {
    if (!operationParams || !leverageOperation) return

    try {
      const txHash = await executeLeverage({
        leverageOperation,
        amount: operationParams.amount,
        leverage: operationParams.leverage,
        swapSlippage: operationParams.swapSlippage,
        leverageType: operationParams.leverageType,
        inputCoinType: operationParams.inputCoinType,
        ownerCapId: ownerCapId
      })
      console.log('Increase size successfully:', txHash)
    } catch (error) {
      console.error('Error increase size:', error)
    }
  }

  const handleDecrease = async () => {
    if (!operationParams || !leverageOperation) return
    const percentage =
      Number(balanceInputValue) === maxWithdrawAmount
        ? 1
        : Number((Number(balanceInputValue) / maxWithdrawAmount).toFixed(2))
    leverageDecrease.mutate({
      leverageObligation: position,
      percentage,
      swapSlippage: slippage
    })
  }

  const showPrediction = useMemo(() => {
    return !!balanceInputValue && !isNaN(Number(balanceInputValue))
  }, [balanceInputValue])

  const transactionDetailData = {
    netBorrowApy: {
      from: 1.35,
      to: 2.13
    },
    leverage: 4.3,
    flashBorrowFee: 0.001,
    exposure: {
      amount: 0.16,
      symbol: 'stSUI',
      usdValue: 28.1,
      newExposure: { from: 0.12, to: 0.16, symbol: 'SUI' },
      newDebt: { from: 12, to: 32.99, symbol: 'USDC' }
    },
    liquidationPrice: {
      from: 2.5,
      to: 2.1,
      ltv: {
        from: 50.32,
        to: 43.1
      },
      liqLtv: 75
    }
  }

  return (
    <>
      <div className="px-6 mb-6">
        <Tabs
          value={tab}
          onValueChange={(item) => {
            setTab(item as ManageSizeTabEnum)
            setBalanceInputValue(undefined)
          }}
          orientation="vertical">
          <TabsList
            className="pl-0 w-full grid grid-cols-2"
            aria-label="deposit tabs">
            <TabsTrigger value={ManageSizeTabEnum.Increase}>
              {ManageSizeTabEnum.Increase}
            </TabsTrigger>
            <TabsTrigger value={ManageSizeTabEnum.Decrease}>
              {ManageSizeTabEnum.Decrease}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div className="px-6 pb-6 flex flex-col gap-y-6">
        {tokens && (
          <BalanceInput
            value={balanceInputValue}
            onChange={(value) => {
              setBalanceInputValue(value)
            }}
            tokens={tokens}
            price={
              inputCoinType?.price ? Number(inputCoinType.price) : undefined
            }
            title={
              tab === ManageSizeTabEnum.Increase
                ? 'Deposit asset'
                : 'Withdraw asset'
            }
            showSlider={tab === ManageSizeTabEnum.Decrease}
            available={
              tab === ManageSizeTabEnum.Decrease ? maxWithdrawAmount : undefined
            }
            balanceTitle={
              tab === ManageSizeTabEnum.Increase ? 'Balance' : 'Available'
            }
          />
        )}

        <Button
          variant="secondary"
          onClick={
            tab === ManageSizeTabEnum.Increase ? handleIncrease : handleDecrease
          }
          disabled={isLoading || !leverageParams || !leverageOperation}>
          {isLoading
            ? 'Loading...'
            : tab === ManageSizeTabEnum.Increase
              ? 'Increase'
              : 'Decrease'}
        </Button>
        <div className="flex flex-col gap-y-3 text-xs">
          <span className="flex items-center justify-between">
            <span className="opacity-60">Transaction Settings</span>
            <SlippageSetting slippage={slippage} onConfirm={setSlippage} />
          </span>
          {showPrediction && <TransactionDetail data={transactionDetailData} />}
        </div>
      </div>
    </>
  )
}
