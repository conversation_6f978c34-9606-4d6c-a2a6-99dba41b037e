import { DepositCollapsibleItem } from '@/components/deposit-collapsible-item'
import { TransactionValues } from '@/components/dialogs/transaction-detail'
import { NumberCell } from '@/components/number-cell'
import type { TransactionDetailData } from '../types'
import { useMemo } from 'react'

export const TransactionDetail = ({
  data
}: {
  data: TransactionDetailData
}) => {
  const predictionData = useMemo(() => {
    const dataArray: {
      id: string
      label: string
      value: React.ReactNode
      details?: { id: string; label: string; value: React.ReactNode }[]
    }[] = [
      {
        id: 'netBorrowApy',
        label: 'Net Borrow APY',
        value: (
          <TransactionValues
            className="text-primary"
            from={data.netBorrowApy.from}
            to={data.netBorrowApy.to}
            isPercentage
          />
        )
      },
      {
        id: 'leverage',
        label: 'Leverage',
        value: `${data.leverage}x`
      }
    ]
    if (data.flashBorrowFee) {
      dataArray.push({
        id: 'flashBorrowFee',
        label: 'Flash Borrow Fee',
        value: `${data.flashBorrowFee}%`
      })
    }
    if (data.exposure) {
      dataArray.push({
        id: 'exposure',
        label: 'Exposure',
        value: (
          <span className="flex items-center gap-x-1.5">
            <span className="flex items-center gap-x-0.5">
              <NumberCell
                value={data.exposure.amount}
                suffix={` ${data.exposure.symbol}`}
              />
              <span className="opacity-40">
                {' '}
                ({<NumberCell value={data.exposure.usdValue} prefix="$" />})
              </span>
            </span>
          </span>
        ),
        details: [
          {
            id: 'newExposure',
            label: 'New Exposure',
            value: (
              <span>
                <TransactionValues
                  from={data.exposure.newExposure.from}
                  to={data.exposure.newExposure.to}
                  isUsdValue={false}
                />
                <span> {data.exposure.newExposure.symbol}</span>
              </span>
            )
          },
          {
            id: 'newDebt',
            label: 'New Debt',
            value: (
              <span>
                <TransactionValues
                  from={data.exposure.newDebt.from}
                  to={data.exposure.newDebt.to}
                  isUsdValue={false}
                />
                <span> {data.exposure.newDebt.symbol}</span>
              </span>
            )
          }
        ]
      })
    }
    dataArray.push({
      id: 'liquidationPrice',
      label: 'Liquidation Price',
      value: (
        <span>
          <TransactionValues
            from={data.liquidationPrice.from}
            to={data.liquidationPrice.to}
            isUsdValue={false}
          />
          <span className="opacity-40">
            {' '}
            (
            <NumberCell
              value={
                data.liquidationPrice.from
                  ? ((data.liquidationPrice.to - data.liquidationPrice.from) /
                      data.liquidationPrice.from) *
                    100
                  : 0
              }
              suffix="%"
            />
            )
          </span>
        </span>
      ),
      details: [
        {
          id: 'ltv',
          label: 'LTV',
          value: (
            <TransactionValues
              from={data.liquidationPrice.ltv.from}
              to={data.liquidationPrice.ltv.to}
              isPercentage
            />
          )
        },
        {
          id: 'liquidationLtv',
          label: 'Liquidation LTV',
          value: (
            <span className="text-red">{data.liquidationPrice.liqLtv}%</span>
          )
        }
      ]
    })
    if (data.netValue) {
      dataArray.push({
        id: 'netValue',
        label: 'Net Value',
        value: (
          <TransactionValues from={data.netValue.from} to={data.netValue.to} />
        )
      })
    }
    if (data.ltv) {
      dataArray.push({
        id: 'ltv',
        label: 'LTV',
        value: (
          <TransactionValues
            from={data.ltv.from}
            to={data.ltv.to}
            isPercentage
          />
        ),
        details: [
          {
            id: 'maxLtv',
            label: 'Max.LTV',
            value: <span className="text-primary">{data.ltv.maxLtv}%</span>
          },
          {
            id: 'liquidationLtv',
            label: 'Liq.LTV',
            value: <span className="text-red">{data.ltv.liqLtv}%</span>
          }
        ]
      })
    }
    if (data.refundableFee) {
      dataArray.push({
        id: 'refundableFee',
        label: 'Refundable Fee',
        value: `${data.refundableFee}`
      })
    }
    return dataArray
  }, [data])

  return (
    <>
      {predictionData.map((item) => (
        <DepositCollapsibleItem key={item.id} data={item} />
      ))}
    </>
  )
}
