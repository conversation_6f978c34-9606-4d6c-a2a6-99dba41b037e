{"name": "@pebble-protocol/examples", "version": "0.0.1", "description": "Examples for Pebble Protocol SDK", "type": "module", "scripts": {"example:deposit": "tsx lending/deposit.ts", "example:borrow": "tsx lending/borrow.ts", "example:repay": "tsx lending/repay.ts", "example:withdraw": "tsx lending/withdraw.ts", "example:enter-market": "tsx lending/enter-market.ts", "example:liquidate": "tsx lending/liquidate.ts", "example:list-obligations": "tsx lending/list-obligations.ts", "example:market-detail": "tsx lending/market-detail.ts", "example:market-asset-detail": "tsx lending/market-asset-detail.ts", "example:obligation-detail": "tsx lending/obligation-detail.ts", "example:obligation-operation": "tsx lending/obligation-operation.ts", "example:coin-metadata": "tsx lending/coin-metadata.ts", "example:long-left-with-left": "tsx leverage/long-left-with-left.ts", "example:short-sui": "tsx leverage/short-sui-with-sui.ts", "example:leverage-obligation": "tsx leverage/obligation-details.ts", "example:leverage-summary": "tsx leverage/obligation-summary.ts", "example:leverage-reduce-size": "tsx leverage/size-reduce.ts", "example:leverage-increase-size": "tsx leverage/size-increase.ts", "example:leverage-increase": "tsx leverage/leverage-increase.ts", "example:leverage-reduce": "tsx leverage/leverage-reduce.ts", "example:mint-tokens": "tsx mint-tokens.ts"}, "dependencies": {"@cetusprotocol/aggregator-sdk": "^0.14.2", "@pebble-protocol/pebble-sdk": "workspace:*", "@mysten/sui": "^1.36.1", "dotenv": "^16.0.3"}, "devDependencies": {"@types/node": "^18.16.3", "tsx": "^4.20.4", "typescript": "^5.0.0"}}