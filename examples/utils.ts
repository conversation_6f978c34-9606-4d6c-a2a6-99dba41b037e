import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import { SuiClient } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";

// Initialize keypair from private key environment variable
export function getKeypair(): Ed25519Keypair {
  const privateKey = process.env.PRIVATE_KEY;
  if (!privateKey) {
    throw new Error("PRIVATE_KEY environment variable is required");
  }

  // Handle Sui private key format (suiprivkey1...)
  if (privateKey.startsWith("suiprivkey1")) {
    return Ed25519Keypair.fromSecretKey(privateKey);
  }

  // Handle hex format (with or without 0x prefix)
  const cleanPrivateKey = privateKey.startsWith("0x")
    ? privateKey.slice(2)
    : privateKey;
  return Ed25519Keypair.fromSecretKey(
    Uint8Array.from(Buffer.from(cleanPrivateKey, "hex"))
  );
}

async function getCoinsForAmount(
    client: Sui<PERSON>lient, 
    address: string, 
    coinType: string, 
    amount: bigint
) {
    const coins = await client.getCoins({
        owner: address,
        coinType: coinType
    });
    
    let totalAmount = 0n;
    const selectedCoins = [];
    
    for (const coin of coins.data) {
        selectedCoins.push(coin);
        totalAmount += BigInt(coin.balance);
        
        if (totalAmount >= amount) break;
    }
    
    return { coins: selectedCoins, totalAmount };
}

export async function prepareCoinsForAmount(
    txb: Transaction,
    client: SuiClient,
    address: string,
    coinType: string,
    requiredAmount: bigint
) {
    // Special handling for SUI to avoid gas issues
    if (coinType === '0x2::sui::SUI' || coinType == "0000000000000000000000000000000000000000000000000000000000000002::sui::SUI") {
        // When dealing with SUI, split from the gas object
        // This assumes gas budget was set appropriately in the caller
        console.log(`Splitting ${requiredAmount} SUI from gas coin`);
        const [exactCoin] = txb.splitCoins(txb.gas, [requiredAmount]);
        return exactCoin;
    }

    // For non-SUI coins, proceed with normal logic
    const { coins, totalAmount } = await getCoinsForAmount(
        client, address, coinType, requiredAmount
    );
    
    console.log(coins, totalAmount);

    if (totalAmount < requiredAmount) {
        throw new Error('Insufficient balance');
    }
    
    // If we have exactly one coin with enough balance
    if (coins.length === 1 && BigInt(coins[0].balance) >= requiredAmount) {
        if (BigInt(coins[0].balance) === requiredAmount) {
            return coins[0].coinObjectId;
        } else {
            // Split the coin to get exact amount
            const [exactCoin] = txb.splitCoins(coins[0].coinObjectId, [requiredAmount]);
            return exactCoin;
        }
    }
    
    // If we need to merge multiple coins
    const primaryCoin = coins[0].coinObjectId;
    const coinsToMerge = coins.slice(1).map(c => c.coinObjectId);
    
    if (coinsToMerge.length > 0) {
        txb.mergeCoins(primaryCoin, coinsToMerge);
    }

    // Split to get exact amount if needed
    if (totalAmount > requiredAmount) {
        const [exactCoin] = txb.splitCoins(primaryCoin, [requiredAmount]);
        return exactCoin;
    }
    
    return primaryCoin;
}

export async function estimateGasForTransaction(
    tx: Transaction,
    client: SuiClient,
): Promise<bigint> {
    const dryRun = await client.dryRunTransactionBlock({
        transactionBlock: await tx.build({ client }),
    });
    
    const gasUsed = BigInt(dryRun.effects.gasUsed.computationCost) + 
                    BigInt(dryRun.effects.gasUsed.storageCost) - 
                    BigInt(dryRun.effects.gasUsed.storageRebate || 0);
    
    // Add 50% buffer for safety
    return (gasUsed * 150n) / 100n;
}