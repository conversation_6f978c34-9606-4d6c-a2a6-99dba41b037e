import { Transaction } from '@mysten/sui/transactions';
import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { PebbleClient, LeverageClient, getNetworkConfig, Decimal } from '@pebble-protocol/pebble-sdk';
import { getKeypair, prepareCoinsForAmount, estimateGasForTransaction } from '../utils';
import { LeverageOperation } from '@pebble-protocol/pebble-sdk/dist/src/leverage-types';

async function longSuiWithSui(
  amount: bigint,
  leverage: number,
  targetLTV: number = 0.7,
  swapSlippage: number = 0.01,
  network: string = "mainnet"
) {
  const client = PebbleClient.fromNetwork(network);
  
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();

  const networkConfig = getNetworkConfig(network);
  const leverageMarket = networkConfig.leverageMarkets[0]!;

  const leverageClient = new LeverageClient(
    new AggregatorClient({}),
    client,
    { leverageMarketId: leverageMarket.objectId, leveragePackageId: networkConfig.leveragePackageId }
  );
  
  console.log(`Sender: ${sender}`);
  console.log(`Network: ${network}`);
  console.log(`Leverage Market: ${leverageMarket.objectId}`);
  console.log(`Amount: ${amount}`);
  console.log(`Leverage: ${leverage}x`);
  console.log(`Target LTV: ${targetLTV}`);
  console.log(`Swap Slippage: ${swapSlippage * 100}%`);
  
  const leftCoinType = '0000000000000000000000000000000000000000000000000000000000000002::sui::SUI';
  const rightCoinType = 'dba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC';
  const inputCoinType = leftCoinType;
  
  const market = await leverageClient.obtainUnderlyingMarket();

  const leverageOperation = LeverageOperation.short(
    market,
    Decimal.fromNumber(targetLTV),
    inputCoinType,
    amount,
    Decimal.fromNumber(leverage),
    leftCoinType,
    rightCoinType,
    Decimal.fromNumber(swapSlippage),
  );
  
  console.log(`Maximum leverage available: ${leverageOperation.maxLeverage.asNumber()}x`);
  
  if (leverage > leverageOperation.maxLeverage.asNumber()) {
    throw new Error(`Requested leverage ${leverage}x exceeds maximum ${leverageOperation.maxLeverage.asNumber()}x`);
  }

  console.log(`Total collateral amount: ${leverageOperation.collateralAmount}`);
  console.log(`Total debt amount: ${leverageOperation.debtAmount}`);
  
  const tx = new Transaction();  
  if (inputCoinType === '0000000000000000000000000000000000000000000000000000000000000002::sui::SUI') {
    const dummyTx = new Transaction();
    dummyTx.setSender(sender);
    const dummyCoin = dummyTx.splitCoins(dummyTx.gas, [amount]);
    
    await leverageClient.openAndApplyOperation(
      dummyTx,

      leverageOperation,

      swapSlippage,
      keypair,

      dummyCoin,
    );
  
    const estimatedGas = await estimateGasForTransaction(dummyTx, client.provider);
    tx.setGasBudget(amount + estimatedGas);
    console.log(`Gas budget set to: ${amount + estimatedGas} (deposit: ${amount}, gas: ${estimatedGas})`);
  }

  const coinObjectId = await prepareCoinsForAmount(tx, client.provider, sender, inputCoinType, amount);  
  await leverageClient.openAndApplyOperation(
    tx,

    leverageOperation,

    swapSlippage,
    keypair,
    
    coinObjectId
  );
  const result = await client.provider.signAndExecuteTransaction(
    {
      transaction: tx,
      signer: keypair,
    }
  );
  console.log(`\nLeverage long position opened successfully!`);
  console.log(`Transaction hash: ${result.digest}`);
}

if (import.meta.url.startsWith('file:')) {
  const amount = 300_000_000n; // 0.3 SUI
  const leverage = 2; // 2x leverage
  const targetLTV = 0.8; // 70% LTV
  const swapSlippage = 0.01; // 1% slippage
  
  longSuiWithSui(amount, leverage, targetLTV, swapSlippage, "mainnet")
    .catch(error => {
      console.error('Error opening long position:', error);
      process.exit(1);
    });
}