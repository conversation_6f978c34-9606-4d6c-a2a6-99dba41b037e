import { Transaction } from '@mysten/sui/transactions';
import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { PebbleClient, LeverageClient, getNetworkConfig, Decimal } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

async function run(
  swapSlippage: number = 0.01,
  network: string = "mainnet"
) {
  const client = PebbleClient.fromNetwork(network);
  
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();

  const networkConfig = getNetworkConfig(network);
  const leverageMarket = networkConfig.leverageMarkets[0]!;

  const leverageOwnerCapId = "0x03f7f59a63f110f14a4bd44a835cf331fff9c4043415452ce28b5fcff4c6d516";
  const leverageClient = new LeverageClient(
    new AggregatorClient({}),
    client,
    { leverageMarketId: leverageMarket.objectId, leveragePackageId: networkConfig.leveragePackageId }
  );

  console.log(`Sender: ${sender}`);
  console.log(`Network: ${network}`);
  console.log(`Leverage Market: ${leverageMarket.objectId}`);
  console.log(`Swap Slippage: ${swapSlippage * 100}%`);

  const market = await leverageClient.obtainUnderlyingMarket();
  const position = await leverageClient.getObligationSummary(leverageOwnerCapId);

  const newLeverage = Decimal.fromNumber(1.3);
  
  const tx = new Transaction();  
  await leverageClient.reduceLeverage(
    tx,
    market,
    leverageOwnerCapId,
    position,
    newLeverage,
    swapSlippage,
    keypair
  );
  const result = await client.provider.signAndExecuteTransaction(
    {
      transaction: tx,
      signer: keypair,
    }
  );
  console.log(`Transaction hash: ${result.digest}`);
}

if (import.meta.url.startsWith('file:')) {
  const swapSlippage = 0.01;
  
  run(swapSlippage, "mainnet")
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}