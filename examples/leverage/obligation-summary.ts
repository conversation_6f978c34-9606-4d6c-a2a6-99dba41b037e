import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { PebbleClient, LeverageClient, getNetworkConfig } from '@pebble-protocol/pebble-sdk';

async function listObligationDetails(
  network: string = "mainnet",
) {
  const client = PebbleClient.fromNetwork(network);

  const networkConfig = getNetworkConfig(network);
  const leverageMarket = networkConfig.leverageMarkets[0];

  const leverageConfig = {
    leverageMarketId: leverageMarket.objectId,
    leveragePackageId: networkConfig.leveragePackageId,
  };

  const leverageClient = new LeverageClient(
    new AggregatorClient({}),
    client,
    leverageConfig
  );

  const ownerCap = "0x03f7f59a63f110f14a4bd44a835cf331fff9c4043415452ce28b5fcff4c6d516";
  const position = await leverageClient.getObligationSummary(ownerCap);
  console.log("leverage:", position.leverageAsFixedOne());

  console.log("position:", position.debtAmount(), position.averagePrice().asNumber(), position.principleAmount(), position.collateralAmount());
  console.log("  debt:", position.debtAmount());
  console.log("  average price:", position.averagePrice().asNumber());
  console.log("  principle amount:", position.principleAmount());
  console.log("  collateral amount:", position.collateralAmount());

  const market = await leverageClient.obtainUnderlyingMarket();
  console.log("pnl: ", position.pnl(market).asNumber());

}

if (import.meta.url.startsWith('file:')) {
  listObligationDetails("mainnet")
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}